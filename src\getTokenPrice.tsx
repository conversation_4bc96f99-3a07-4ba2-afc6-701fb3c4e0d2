
const clientId = import.meta.env.VITE_TEMPLATE_CLIENT_ID;

export async function getTokenPrice({
  tokenAddress = "ETH", // You can use a stablecoin address to get USD price
  chainId = 11155111, //137, // Polygon mainnet
}) {
  const res = await fetch(
    `https://bridge.thirdweb.com/v1/tokens?chainId=${chainId}&tokenAddress=${tokenAddress}`,
    {
      headers: {
        "x-client-id": clientId,
      },
    }
  );

  const data = await res.json();
  const price = data.data[0].priceUsd;
  return price; // data.displayValue contains the price
}
