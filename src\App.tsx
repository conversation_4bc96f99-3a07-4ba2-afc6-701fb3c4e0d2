
import { ConnectButton } from "thirdweb/react";
import { createWallet, inAppWallet } from "thirdweb/wallets";
import { client } from "./client";
import { TransactionButton, useActiveAccount } from "thirdweb/react";
import { useActiveWallet, useSendTransaction } from "thirdweb/react";
import { useEffect, useState } from "react";
import { sepolia } from "thirdweb/chains";
import { getWalletBalance } from "thirdweb/wallets";

import { toWei, getContract, prepareContractCall, readContract } from "thirdweb";
import { getTokenPrice } from "./getTokenPrice";

import ethLogo from "./assets/eth_logo.png";
import ballsLogo from "./assets/balls.png";

const CONTRACT_ADDRESS = import.meta.env.VITE_BALLS_CONTRACT_ADDRESS;

const wallets = [
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("me.rainbow"),
];

// Set up the contract
const contract = getContract({
  address: CONTRACT_ADDRESS,
  client: client,
  chain: sepolia,
});

export function App() {
  const [tokenQuantity, setTokenQuantity] = useState("0.01");
  const [dollarPrice, setDollarPrice] = useState(1);
  const [dollarDisplayPrice, setDollarDisplayPrice] = useState("");
  const [tokenBalance, setTokenBalance] = useState("");
  const [ballsReceived, setBallsReceived] = useState("");
  const [totalDollarDisplayPrice, settotalDollarDisplayPrice] = useState("1");
  const [ballsPerEth, setBallsPerEth] = useState("");

  const account = useActiveAccount();

  // Debug function to test contract
  const debugContract = async () => {
    console.log("Contract address:", CONTRACT_ADDRESS);
    console.log("Token quantity:", tokenQuantity);
    console.log("Wei amount:", toWei(tokenQuantity));
    console.log("Account:", account?.address);
  };

  useEffect(() => {
    if (account?.address) {
      console.log("Account address:", account.address);
      getTokenBalance();
      debugContract();
    }
  }, [account, tokenQuantity]);

  const getTokenBalance = async () => {
    if (!account?.address) return; // safely exit if undefined
    try {
      const balance = await getWalletBalance({
        address: account.address,
        client: client,
        chain: sepolia,
      });

      // setTokenBalance(balance.displayValue);
      console.log("Token balance:", balance.displayValue);
      setTokenBalance(balance.displayValue);
    } catch (error) {
      console.error("Error fetching balance:", error);
    }
  };

  // For native token balance (MATIC, etc.)
  //   const getNativeBalance = async () => {
  //     try {
  //       const balance = await getWalletBalance({
  //         address: account.address,
  //         client: client,
  //         chain: ethereum
  //         // No tokenAddress = native token
  //       });

  //       console.log("Native balance:", balance.displayValue);
  //     } catch (error) {
  //       console.error("Error fetching native balance:", error);
  //     }
  //   };

  useEffect(() => {
    async function fetchPrice() {
      try {
        // const priceData = await getTokenPrice({
        //   tokenAddress: "******************************************",
        // });
        const priceData = 1;
        console.log(priceData);
        setDollarPrice(priceData);
        const formattedPrice = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2, // optional, useful for tokens
        }).format(priceData);
        setDollarDisplayPrice(formattedPrice);
      } catch (err) {
        console.error("Error fetching token price:", err);
      }
    }

    fetchPrice();
  }, []);

  useEffect(() => {
    async function calculateReceivedTokens() {
      try {
        const balls = Number(tokenQuantity) * dollarPrice * 5;
        console.log("balls", balls);
        setBallsReceived(balls.toFixed(2));
      } catch (err) {
        console.error("Error calculating balls:", err);
      }
    }

    calculateReceivedTokens();
  }, [tokenQuantity, dollarPrice]);

  useEffect(() => {
    async function calculateTotalDollars() {
      try {
        const totalPrice = Number(tokenQuantity) * dollarPrice;
        console.log("totalPrice", totalPrice);
        settotalDollarDisplayPrice(totalPrice.toString());
      } catch (err) {
        console.error("Error calculating totalPrice:", err);
      }
    }

    calculateTotalDollars();
  }, [tokenQuantity, dollarPrice]);

  useEffect(() => {
    async function calculateBallsPerEth() {
      try {
        const ballsPerEth = 5 * dollarPrice;
        console.log("ballsPerEth", ballsPerEth);
        setBallsPerEth(ballsPerEth.toFixed(2));
      } catch (err) {
        console.error("Error calculating totalPrice:", err);
      }
    }

    calculateBallsPerEth();
  }, [dollarPrice]);

  return (
    <main className="p-4 pb-10 min-h-[100vh] flex items-center justify-center container max-w-screen-lg mx-auto">
      <div className="py-20 w-full max-w-md text-center">
        <div className="buy-container">
          <div className="buy-header">BUY $BALLS</div>
          <div className="input-group">
            <div>
              <div>
                <input
                  type="string"
                  placeholder="Enter ETH amount"
                  value={tokenQuantity}
                  onChange={(e) => setTokenQuantity(e.target.value)}
                  className="w-full p-3 border rounded-lg"
                />
              </div>
              <div className="network-section">
                <div className="price-note" id="price-note">
                  1 ETH = {dollarDisplayPrice}
                </div>
              </div>
            </div>
            <div>
              <img src={ethLogo} width="32" alt="ETH logo" />
            </div>
            <div>
              Your ETH Balance: <span id="token_balance">{tokenBalance}</span>
            </div>
          </div>

          <div className="output-group">
            <div>Receive BALLS Token:</div>
            <div>
              <img src={ballsLogo} width="32" alt="Balls logo" />
              <h4>
                <span id="number_of_balls">{ballsReceived}</span>
              </h4>
            </div>
          </div>

          <div className="exchange-info">
            <div>💵 Spending: {totalDollarDisplayPrice} USD</div>
            <div>🔄 1 ETH = {ballsPerEth} BALLS</div>
          </div>
        </div>

        <ConnectButton client={client} wallets={wallets} />

        <div className="mt-10 space-y-4">
          {/* Try 1: Simple mint() function */}
          <TransactionButton
            transaction={() => {
              console.log("Trying simple mint() with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function mint() payable",
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Simple mint() failed:", err);
              alert(`Simple mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Simple mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Simple mint confirmed!");
              console.log(tx);
            }}
          >
            Try Simple Mint()
          </TransactionButton>

          {/* Try 2: Mint with parameters */}
          <TransactionButton
            transaction={() => {
              console.log("Trying mint(address, amount) with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function mint(address to, uint256 amount) payable",
                params: [account?.address || "0x0", BigInt(1)],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Parametered mint failed:", err);
              alert(`Parametered mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Parametered mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Parametered mint confirmed!");
              console.log(tx);
            }}
          >
            Try Mint(address, amount)
          </TransactionButton>

          {/* Try 3: Public mint */}
          <TransactionButton
            transaction={() => {
              console.log("Trying publicMint() with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function publicMint(uint256 quantity) payable",
                params: [BigInt(1)],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Public mint failed:", err);
              alert(`Public mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Public mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Public mint confirmed!");
              console.log(tx);
            }}
          >
            Try PublicMint(quantity)
          </TransactionButton>
        </div>
      </div>
    </main>
  );
}
