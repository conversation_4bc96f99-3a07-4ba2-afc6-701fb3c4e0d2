
import { ConnectButton } from "thirdweb/react";
import { createWallet, inAppWallet } from "thirdweb/wallets";
import { client } from "./client";
import { TransactionButton, useActiveAccount } from "thirdweb/react";
import { useActiveWallet, useSendTransaction } from "thirdweb/react";
import { useEffect, useState } from "react";
import { sepolia } from "thirdweb/chains";
import { getWalletBalance } from "thirdweb/wallets";

import { toWei, getContract, prepareContractCall, readContract, prepareTransaction } from "thirdweb";
import { getTokenPrice } from "./getTokenPrice";

import ethLogo from "./assets/eth_logo.png";
import ballsLogo from "./assets/balls.png";

const CONTRACT_ADDRESS = import.meta.env.VITE_BALLS_CONTRACT_ADDRESS;

const wallets = [
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("me.rainbow"),
];

// Set up the contract
const contract = getContract({
  address: CONTRACT_ADDRESS,
  client: client,
  chain: sepolia,
});

export function App() {
  const [tokenQuantity, setTokenQuantity] = useState("0.01");
  const [dollarPrice, setDollarPrice] = useState(1);
  const [dollarDisplayPrice, setDollarDisplayPrice] = useState("");
  const [tokenBalance, setTokenBalance] = useState("");
  const [ballsReceived, setBallsReceived] = useState("");
  const [totalDollarDisplayPrice, settotalDollarDisplayPrice] = useState("1");
  const [ballsPerEth, setBallsPerEth] = useState("");

  const account = useActiveAccount();

  // Debug function to test contract
  const debugContract = async () => {
    console.log("Contract address:", CONTRACT_ADDRESS);
    console.log("Contract address length:", CONTRACT_ADDRESS?.length);
    console.log("Is valid address format:", /^0x[a-fA-F0-9]{40}$/.test(CONTRACT_ADDRESS || ""));
    console.log("Token quantity:", tokenQuantity);
    console.log("Wei amount:", toWei(tokenQuantity));
    console.log("Account:", account?.address);

    // Try to read some basic contract info
    try {
      // Try to read common ERC721/ERC1155 functions
      const name = await readContract({
        contract: contract,
        method: "function name() view returns (string)",
      });
      console.log("Contract name:", name);
    } catch (e) {
      console.log("Could not read contract name:", e);
    }

    try {
      const totalSupply = await readContract({
        contract: contract,
        method: "function totalSupply() view returns (uint256)",
      });
      console.log("Total supply:", totalSupply);
    } catch (e) {
      console.log("Could not read total supply:", e);
    }

    try {
      const price = await readContract({
        contract: contract,
        method: "function price() view returns (uint256)",
      });
      console.log("Mint price:", price);
    } catch (e) {
      console.log("Could not read price:", e);
    }

    // Try to check if contract exists by reading a basic function
    try {
      // If we can read any function, the contract exists
      await readContract({
        contract: contract,
        method: "function owner() view returns (address)",
      });
      console.log("✅ Contract appears to be deployed (has owner function)");
    } catch (e) {
      console.log("Could not read owner function - contract might not be deployed or might not have owner():", e);
    }

    // Check your ETH balance
    if (account?.address) {
      try {
        const balance = await getWalletBalance({
          address: account.address,
          client: client,
          chain: sepolia,
        });
        console.log("Your ETH balance:", balance.displayValue, "ETH");
        const balanceNum = parseFloat(balance.displayValue);
        const requiredAmount = parseFloat(tokenQuantity);
        if (balanceNum < requiredAmount) {
          console.error(`⚠️ INSUFFICIENT BALANCE! You have ${balanceNum} ETH but trying to send ${requiredAmount} ETH`);
        }
      } catch (e) {
        console.log("Could not check ETH balance:", e);
      }
    }
  };

  useEffect(() => {
    if (account?.address) {
      console.log("Account address:", account.address);
      getTokenBalance();
      debugContract();
    }
  }, [account, tokenQuantity]);

  const getTokenBalance = async () => {
    if (!account?.address) return; // safely exit if undefined
    try {
      const balance = await getWalletBalance({
        address: account.address,
        client: client,
        chain: sepolia,
      });

      // setTokenBalance(balance.displayValue);
      console.log("Token balance:", balance.displayValue);
      setTokenBalance(balance.displayValue);
    } catch (error) {
      console.error("Error fetching balance:", error);
    }
  };

  // For native token balance (MATIC, etc.)
  //   const getNativeBalance = async () => {
  //     try {
  //       const balance = await getWalletBalance({
  //         address: account.address,
  //         client: client,
  //         chain: ethereum
  //         // No tokenAddress = native token
  //       });

  //       console.log("Native balance:", balance.displayValue);
  //     } catch (error) {
  //       console.error("Error fetching native balance:", error);
  //     }
  //   };

  useEffect(() => {
    async function fetchPrice() {
      try {
        // const priceData = await getTokenPrice({
        //   tokenAddress: "******************************************",
        // });
        const priceData = 1;
        console.log(priceData);
        setDollarPrice(priceData);
        const formattedPrice = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2, // optional, useful for tokens
        }).format(priceData);
        setDollarDisplayPrice(formattedPrice);
      } catch (err) {
        console.error("Error fetching token price:", err);
      }
    }

    fetchPrice();
  }, []);

  useEffect(() => {
    async function calculateReceivedTokens() {
      try {
        const balls = Number(tokenQuantity) * dollarPrice * 5;
        console.log("balls", balls);
        setBallsReceived(balls.toFixed(2));
      } catch (err) {
        console.error("Error calculating balls:", err);
      }
    }

    calculateReceivedTokens();
  }, [tokenQuantity, dollarPrice]);

  useEffect(() => {
    async function calculateTotalDollars() {
      try {
        const totalPrice = Number(tokenQuantity) * dollarPrice;
        console.log("totalPrice", totalPrice);
        settotalDollarDisplayPrice(totalPrice.toString());
      } catch (err) {
        console.error("Error calculating totalPrice:", err);
      }
    }

    calculateTotalDollars();
  }, [tokenQuantity, dollarPrice]);

  useEffect(() => {
    async function calculateBallsPerEth() {
      try {
        const ballsPerEth = 5 * dollarPrice;
        console.log("ballsPerEth", ballsPerEth);
        setBallsPerEth(ballsPerEth.toFixed(2));
      } catch (err) {
        console.error("Error calculating totalPrice:", err);
      }
    }

    calculateBallsPerEth();
  }, [dollarPrice]);

  return (
    <main className="p-4 pb-10 min-h-[100vh] flex items-center justify-center container max-w-screen-lg mx-auto">
      <div className="py-20 w-full max-w-md text-center">
        <div className="buy-container">
          <div className="buy-header">BUY $BALLS</div>
          <div className="input-group">
            <div>
              <div>
                <input
                  type="string"
                  placeholder="Enter ETH amount"
                  value={tokenQuantity}
                  onChange={(e) => setTokenQuantity(e.target.value)}
                  className="w-full p-3 border rounded-lg"
                />
              </div>
              <div className="network-section">
                <div className="price-note" id="price-note">
                  1 ETH = {dollarDisplayPrice}
                </div>
              </div>
            </div>
            <div>
              <img src={ethLogo} width="32" alt="ETH logo" />
            </div>
            <div>
              Your ETH Balance: <span id="token_balance">{tokenBalance}</span>
            </div>
          </div>

          <div className="output-group">
            <div>Receive BALLS Token:</div>
            <div>
              <img src={ballsLogo} width="32" alt="Balls logo" />
              <h4>
                <span id="number_of_balls">{ballsReceived}</span>
              </h4>
            </div>
          </div>

          <div className="exchange-info">
            <div>💵 Spending: {totalDollarDisplayPrice} USD</div>
            <div>🔄 1 ETH = {ballsPerEth} BALLS</div>
          </div>
        </div>

        <ConnectButton client={client} wallets={wallets} />

        <div className="mt-10 space-y-4">
          {/* Debug button */}
          <button
            onClick={debugContract}
            className="w-full p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            🔍 Debug Contract Info
          </button>

          {/* Simple ETH transfer to test if address exists */}
          <TransactionButton
            transaction={() => {
              console.log("Trying simple ETH transfer to:", CONTRACT_ADDRESS);
              return prepareTransaction({
                to: CONTRACT_ADDRESS,
                value: toWei(tokenQuantity),
                chain: sepolia,
                client: client,
              });
            }}
            onError={(err) => {
              console.error("ETH transfer failed:", err);
              alert(`ETH transfer failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("ETH transfer sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("ETH transfer confirmed!");
              console.log(tx);
            }}
          >
            💰 Try Simple ETH Transfer
          </TransactionButton>
          {/* Try 1: Simple mint() function */}
          <TransactionButton
            transaction={() => {
              console.log("Trying simple mint() with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function mint() payable",
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Simple mint() failed:", err);
              alert(`Simple mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Simple mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Simple mint confirmed!");
              console.log(tx);
            }}
          >
            Try Simple Mint()
          </TransactionButton>

          {/* Try 2: Mint with parameters */}
          <TransactionButton
            transaction={() => {
              console.log("Trying mint(address, amount) with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function mint(address to, uint256 amount) payable",
                params: [account?.address || "0x0", BigInt(1)],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Parametered mint failed:", err);
              alert(`Parametered mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Parametered mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Parametered mint confirmed!");
              console.log(tx);
            }}
          >
            Try Mint(address, amount)
          </TransactionButton>

          {/* Try 3: Public mint */}
          <TransactionButton
            transaction={() => {
              console.log("Trying publicMint() with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function publicMint(uint256 quantity) payable",
                params: [BigInt(1)],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Public mint failed:", err);
              alert(`Public mint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Public mint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Public mint confirmed!");
              console.log(tx);
            }}
          >
            Try PublicMint(quantity)
          </TransactionButton>

          {/* Try 4: Mint with quantity only */}
          <TransactionButton
            transaction={() => {
              console.log("Trying mint(uint256) with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function mint(uint256 quantity) payable",
                params: [BigInt(1)],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("Mint(quantity) failed:", err);
              alert(`Mint(quantity) failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("Mint(quantity) transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("Mint(quantity) confirmed!");
              console.log(tx);
            }}
          >
            Try Mint(quantity)
          </TransactionButton>

          {/* Try 5: safeMint */}
          <TransactionButton
            transaction={() => {
              console.log("Trying safeMint() with amount:", tokenQuantity, "ETH");
              return prepareContractCall({
                contract: contract,
                method: "function safeMint(address to) payable",
                params: [account?.address || "0x0"],
                value: toWei(tokenQuantity),
              });
            }}
            onError={(err) => {
              console.error("SafeMint failed:", err);
              alert(`SafeMint failed: ${err.message}`);
            }}
            onTransactionSent={(tx) => {
              alert("SafeMint transaction sent!");
            }}
            onTransactionConfirmed={(tx) => {
              alert("SafeMint confirmed!");
              console.log(tx);
            }}
          >
            Try SafeMint(address)
          </TransactionButton>
        </div>
      </div>
    </main>
  );
}
